# Technology Stack

## Core Framework
- **React 18.3.1** - Modern React with hooks and concurrent features
- **TypeScript 5.5.3** - Type-safe development
- **Vite 5.4.2** - Fast build tool and dev server

## Styling & UI
- **Tailwind CSS 3.4.1** - Utility-first CSS framework
- **Framer Motion 12.5.0** - Animation library
- **Lucide React 0.344.0** - Icon library
- **PostCSS 8.4.35** - CSS processing

## PDF Generation
- **@react-pdf/renderer 3.1.14** - PDF document generation
- **Semantic design system** - Centralized PDF styling architecture

## Routing & Meta
- **React Router DOM 6.22.3** - Client-side routing
- **React Helmet Async 2.0.5** - Document head management

## Development Tools
- **ESLint 9.9.1** - Code linting
- **TypeScript ESLint 8.3.0** - TypeScript-specific linting
- **Cross-env 7.0.3** - Environment variable management

## Architecture Pattern
- **Component-based** - Modular UI components
- **Section-driven** - Numbered sections (10-home, 20-about, etc.)
- **Three-tier design** - UI components, sections, pages
- **Domain-driven organization** - Functionality-based grouping
- **MCP isolation** - Meta utilities architecturally isolated from main site

## Key Features
- **Arbeidskontrakt Generator** - Legal employment contract generation
- **Seasonal content system** - Dynamic seasonal logic
- **Filtering system** - Centralized filtering utilities
- **SEO optimization** - Meta tags and analytics
- **Contact form** - Enhanced with analytics
- **Responsive design** - Mobile-first approach
