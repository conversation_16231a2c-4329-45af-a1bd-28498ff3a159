import React from 'react';
import { cn  } from '@/lib/utils';
import Container from '@/ui/Container';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  message?: string;
  fullScreen?: boolean;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  className,
  message,
  fullScreen = false
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const spinner = (
    <div
      className={cn(
        'inline-flex items-center justify-center',
        className
      )}
      role="status"
      aria-label="Laster..."
    >
      <svg
        className={cn(
          'animate-spin text-green-500',
          sizeClasses[size]
        )}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      <span className="sr-only">Laster...</span>
    </div>
  );

  // If a message is provided, show a more prominent loading indicator
  if (message) {
    const content = (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="mb-4">
          {spinner}
        </div>
        <p className="text-gray-600">{message}</p>
      </div>
    );

    if (fullScreen) {
      return (
        <div className="fixed inset-0 bg-white bg-opacity-90 z-50 flex items-center justify-center">
          {content}
        </div>
      );
    }

    return <Container>{content}</Container>;
  }

  // Default simple spinner
  return spinner;
};

export { Loading };
export default Loading;