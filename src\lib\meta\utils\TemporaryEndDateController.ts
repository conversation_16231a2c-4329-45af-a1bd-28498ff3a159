/**
 * Temporary End Date Controller - Smart date input logic for temporary employment end date fields
 * Handles arrow key navigation, start date constraints, and smart initialization
 */

export interface TemporaryEndDateConstraints {
  minYear: number;  // 2015 (company founding year)
  maxYear: number;  // currentYear + 10 (planning horizon)
  currentYear: number;
  startDate?: string; // The employment start date that this end date must be after
}

export class TemporaryEndDateController {
  private constraints: TemporaryEndDateConstraints;

  constructor(startDate?: string) {
    const currentYear = new Date().getFullYear();
    
    this.constraints = {
      minYear: 2015,  // Company was founded in 2015
      maxYear: currentYear + 10,  // 10 years into future
      currentYear,
      startDate
    };
  }

  /**
   * Updates the start date constraint
   */
  updateStartDate(startDate?: string) {
    this.constraints.startDate = startDate;
  }

  /**
   * Handles arrow key navigation for date fields
   * @param key - The pressed key ('ArrowUp' or 'ArrowDown')
   * @param currentValue - Current input value
   * @param _cursorPosition - Current cursor position in input (unused for HTML5 inputs)
   * @returns New date string or null if no change needed
   */
  handleArrowKeyNavigation(
    key: string, 
    currentValue: string, 
    _cursorPosition: number
  ): string | null {
    // Only handle arrow keys
    if (!['ArrowUp', 'ArrowDown'].includes(key)) {
      return null;
    }

    // For HTML5 date inputs, let the browser handle all navigation natively
    // This allows proper day/month/year navigation without interference
    // We only intercept when the field is empty to provide smart initialization
    
    if (!currentValue || currentValue === '') {
      // Initialize empty field with smart default (day after start date or today)
      const smartDefault = this.getSmartDefaultDate();
      return this.formatDateForInput(smartDefault);
    }

    // For non-empty fields, let browser handle navigation natively
    return null;
  }

  /**
   * Validates and corrects a date input
   * @param inputValue - The input value to validate
   * @returns Corrected date string or original if valid
   */
  validateAndCorrect(inputValue: string): string {
    if (!inputValue) return inputValue;

    const date = this.parseInputValue(inputValue);
    if (!date) return inputValue;

    // Check if date is before start date
    if (this.constraints.startDate) {
      const startDate = new Date(this.constraints.startDate);
      if (date <= startDate) {
        // Set to day after start date
        const nextDay = new Date(startDate);
        nextDay.setDate(startDate.getDate() + 1);
        return this.formatDateForInput(nextDay);
      }
    }

    const year = date.getFullYear();
    
    // Clamp year to valid business range
    const correctedYear = Math.max(
      this.constraints.minYear,
      Math.min(year, this.constraints.maxYear)
    );

    if (correctedYear !== year) {
      const correctedDate = new Date(date);
      correctedDate.setFullYear(correctedYear);
      return this.formatDateForInput(correctedDate);
    }

    return inputValue;
  }

  /**
   * Gets HTML attributes for the input field
   */
  getHTMLAttributes() {
    const minDate = this.constraints.startDate || `${this.constraints.minYear}-01-01`;
    
    return {
      min: minDate,
      max: `${this.constraints.maxYear}-12-31`,
      placeholder: 'mm/dd/yyyy'
    };
  }

  /**
   * Gets smart default date for temporary end date
   */
  private getSmartDefaultDate(): Date {
    if (this.constraints.startDate) {
      // Default to one day after start date
      const startDate = new Date(this.constraints.startDate);
      const nextDay = new Date(startDate);
      nextDay.setDate(startDate.getDate() + 1);
      return nextDay;
    }
    
    // Fallback to today if no start date
    return new Date();
  }

  /**
   * Parses input value to Date object
   */
  private parseInputValue(value: string): Date | null {
    if (!value) return null;
    
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Formats date for HTML date input (yyyy-mm-dd)
   */
  private formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Gets current constraints
   */
  getConstraints(): TemporaryEndDateConstraints {
    return { ...this.constraints };
  }
}
