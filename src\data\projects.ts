/**
 * @deprecated This file contains static project data that is now managed through the API layer.
 * Please use the API functions in src/lib/api/index.ts instead.
 */

import { ProjectType } from '@/lib/types';
import { getImagePathWithFallback } from '@/lib/utils/images';

// Static project data for reference
export const recentProjects: ProjectType[] = [
  {
    id: "moderne-hage-royse",
    title: "Moderne Hage på Røyse",
    description: "En komplett hagefornyelse med cortenstål, ferdigplen og moderne beplantning.",
    location: "Røyse",
    completionDate: "Oktober 2023",
    image: getImagePathWithFallback('stål', 'IMG_3847.webp'),
    category: "Cortenstål",
    tags: ["moderne", "cortenstål", "belysning"],
    specifications: {
      size: "280m²",
      duration: "6 uker",
      materials: ["Cortenstål", "LED-belysning", "Ferdigplen"],
      features: ["Automatisk vanning", "Smart belysning"]
    },
    testimonial: {
      quote: "Fantastisk arbeid med vår nye hage. Profesjonelt team som leverte over forventning.",
      author: "<PERSON>"
    }
  },
  {
    id: "terrasse-hole",
    title: "Eksklusiv Terrasse",
    description: "Stor terrasse med granitttrapp og integrerte plantekasser i cortenstål.",
    location: "Hole",
    completionDate: "September 2023",
    image: getImagePathWithFallback('platting', 'IMG_4188.webp'),
    category: "Platting",
    tags: ["terrasse", "granitt", "cortenstål"],
    specifications: {
      size: "120m²",
      duration: "4 uker",
      materials: ["Granitt", "Cortenstål"],
      features: ["Integrert belysning"]
    }
  },
  {
    id: "stottemur-honefoss",
    title: "Natursteinmur i Hønefoss",
    description: "En imponerende støttemur i naturstein som løser høydeforskjeller.",
    location: "Hønefoss",
    completionDate: "August 2023",
    image: getImagePathWithFallback('støttemur', 'IMG_2855.webp'),
    category: "Støttemur",
    tags: ["naturstein", "støttemur", "terrengforming"],
    specifications: {
      size: "45 løpemeter",
      duration: "5 uker",
      materials: ["Naturstein"],
      features: ["Integrert trapp"]
    }
  },
  {
    id: "belegningsstein-royse",
    title: "Innkjørsel med belegningsstein",
    description: "Ny innkjørsel med belegningsstein og dekorativt mønster.",
    location: "Røyse",
    completionDate: "Juli 2023",
    image: getImagePathWithFallback('belegg', 'IMG_3037.webp'),
    category: "Belegningsstein",
    tags: ["innkjørsel", "belegningsstein", "mønster"],
    specifications: {
      size: "85m²",
      duration: "3 uker",
      materials: ["Belegningsstein", "Kantstein"],
      features: ["Drenering", "Dekorativt mønster"]
    }
  },
  {
    id: "ferdigplen-vik",
    title: "Ferdigplen i Vik",
    description: "Legging av ferdigplen for en perfekt grønn hage.",
    location: "Vik",
    completionDate: "Juni 2023",
    image: getImagePathWithFallback('ferdigplen', 'IMG_1912.webp'),
    category: "Ferdigplen",
    tags: ["plen", "hage", "grøntareal"],
    specifications: {
      size: "150m²",
      duration: "2 uker",
      materials: ["Ferdigplen", "Jord"],
      features: ["Vanning", "Gjødsling"]
    }
  },
  {
    id: "kantstein-sundvollen",
    title: "Kantstein i Sundvollen",
    description: "Montering av kantstein for å definere områder og skape rene linjer.",
    location: "Sundvollen",
    completionDate: "Mai 2023",
    image: getImagePathWithFallback('kantstein', 'IMG_0716.webp'),
    category: "Kantstein",
    tags: ["kantstein", "hage", "design"],
    specifications: {
      size: "35 løpemeter",
      duration: "1 uke",
      materials: ["Kantstein", "Betong"],
      features: ["Presis montering"]
    }
  },
  {
    id: "trapp-repo-jevnaker",
    title: "Granitttrapp i Jevnaker",
    description: "Bygging av trapp og repo i granitt for en elegant og holdbar løsning.",
    location: "Jevnaker",
    completionDate: "April 2023",
    image: getImagePathWithFallback('trapp-repo', 'IMG_4111.webp'),
    category: "Trapper og Repoer",
    tags: ["trapp", "granitt", "inngang"],
    specifications: {
      size: "12 trinn",
      duration: "2 uker",
      materials: ["Granitt", "Betong"],
      features: ["Sklisikker overflate", "Integrert belysning"]
    }
  },
  {
    id: "hekk-hole",
    title: "Hekk og beplantning i Hole",
    description: "Planting av hekk og annen beplantning for en frodig og vakker hage.",
    location: "Hole",
    completionDate: "Mai 2023",
    image: getImagePathWithFallback('hekk', 'IMG_2370.webp'),
    category: "Hekk og Beplantning",
    tags: ["hekk", "beplantning", "hage"],
    specifications: {
      size: "25 løpemeter",
      duration: "1 uke",
      materials: ["Bøkehekk", "Stauder", "Busker"],
      features: ["Lokaltilpassede planter", "Automatisk vanning"]
    }
  }
];

/**
 * @deprecated These functions are now available through the API layer.
 * Please use the equivalent functions from src/lib/api/index.ts instead.
 *
 * For filtering functionality, use the utilities in src/lib/utils/filtering.ts.
 */

export const getProjectById = (id: string): ProjectType | undefined => {
  console.warn('Deprecated: Use getProjectById from src/lib/api instead');
  return recentProjects.find((project) => project.id === id);
};