/**
 * Formatting utilities for dates, strings, and phone numbers
 */

/**
 * Formats a date into a localized string (Norwegian format)
 * Legacy function - kept for backward compatibility
 */
export const formatDate = (date: string | Date): string => {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return new Intl.DateTimeFormat("no-NO", {
        month: "long",
        year: "numeric",
    }).format(dateObj);
};

/**
 * Formats a date to Norwegian dd.mm.yyyy format
 * Uses Intl.DateTimeFormat for consistent, locale-aware formatting
 *
 * @param date - Date object, ISO string, or date string to format
 * @param options - Optional Intl.DateTimeFormatOptions to override defaults
 * @returns Formatted date string in dd.mm.yyyy format
 *
 * @example
 * formatDateDDMMYYYY(new Date()) // "28.06.2025"
 * formatDateDDMMYYYY("2025-06-28") // "28.06.2025"
 * formatDateDDMMYYYY(new Date(), { year: '2-digit' }) // "28.06.25"
 */
export const formatDateDDMMYYYY = (
    date: Date | string,
    options?: Intl.DateTimeFormatOptions
): string => {
    if (!date) return '';

    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Check if date is valid
    if (isNaN(dateObj.getTime())) return '';

    return dateObj.toLocaleDateString('no-NO', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        ...options
    });
};

/**
 * Formats a date with time to Norwegian dd.mm.yyyy HH:mm format
 *
 * @param date - Date object, ISO string, or date string to format
 * @returns Formatted datetime string
 *
 * @example
 * formatDateTime(new Date()) // "28.06.2025 14:30"
 */
export const formatDateTime = (date: Date | string): string => {
    if (!date) return '';

    const dateObj = typeof date === 'string' ? new Date(date) : date;

    if (isNaN(dateObj.getTime())) return '';

    return dateObj.toLocaleString('no-NO', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};


/**
 * Formats a phone number into a readable format (XXX XX XXX)
 */
export const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length === 8) {
        return cleaned.replace(/(\d{3})(\d{2})(\d{3})/, "$1 $2 $3");
    }
    return phone;
};
