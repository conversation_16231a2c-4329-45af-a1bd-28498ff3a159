# Filestructure-First Approach: Recommendations for Ringerike Landskap Website

This document provides concrete recommendations for maintaining and improving the Ringerike Landskap website codebase through a filestructure-first approach. By treating the directory structure as the fundamental source of truth, we can ensure the codebase remains organized, consistent, and maintainable.

## 1. Guiding Principles

### 1.1. Filestructure as the Single Source of Truth

The directory structure should be the primary reference point for:

1. **Understanding Component Relationships**: The location of files should indicate their relationships and dependencies.
2. **Identifying Module Boundaries**: Directory boundaries should clearly define module boundaries.
3. **Navigating the Codebase**: The directory structure should make it intuitive to find relevant files.
4. **Making Architectural Decisions**: Changes to the architecture should be reflected in the directory structure.

### 1.2. Consistency and Predictability

1. **Consistent Naming Conventions**: Files and directories should follow consistent naming patterns.
2. **Predictable File Locations**: Similar types of files should be located in similar places.
3. **Standard Directory Depths**: Avoid excessive nesting that complicates navigation.

### 1.3. Separation of Concerns

1. **Domain-Specific Directories**: Group related files by domain rather than technical function.
2. **Clear Module Boundaries**: Each module should have a clear responsibility.
3. **Minimal Cross-Directory Dependencies**: Reduce dependencies across directory boundaries.

## 2. Current Filestructure Strengths

The Ringerike Landskap website codebase already demonstrates several strengths in its filestructure:

1. **Chronological Section Organization**: The numerical prefixes (10-home, 20-about) provide a clear order.
2. **Logical Grouping of Related Files**: Files are grouped by functionality rather than technical role.
3. **Clear Separation of UI Components**: Reusable UI components are separated from section-specific components.
4. **Centralized Configuration**: Configuration files are organized in dedicated directories.
5. **Well-Defined Data Flow**: The structure supports a clear data flow from constants to components.

## 3. Areas for Improvement

Based on the detailed analysis of the codebase, the following areas could benefit from filestructure improvements:

### 3.1. Component Organization

**Issue**: Inconsistent component organization with some components in `src/components/` and others in `src/ui/`.

**Recommendation**: Consolidate all reusable UI components under `src/ui/` and use `src/components/` exclusively for specialized, cross-cutting components like SEO or analytics.

```
src/
├── ui/           # All reusable UI components (buttons, cards, etc.)
│   ├── Button/   # Each component in its own directory
│   ├── Card/     # with implementation, tests, and examples
│   └── Form/     # Form-specific components
└── components/   # Specialized, cross-cutting components
    ├── Meta/     # Metadata components
    └── SEO/      # SEO-specific components
```

### 3.2. Data vs. Content Distinction

**Issue**: Unclear distinction between `src/data/` and `src/content/`.

**Recommendation**: Clarify the purpose of each directory and establish clear guidelines for what belongs where:

```
src/
├── data/         # Static, application-wide data (services, projects)
└── content/      # Dynamic, user-facing content (team, locations)
    └── README.md # Document the distinction between data and content
```

### 3.3. Domain-Specific Utilities

**Issue**: Filtering logic spans multiple concerns (services, projects, seasons) in a single file.

**Recommendation**: Organize utility functions by domain to improve maintainability:

```
src/lib/utils/
├── filtering/    # Domain-specific filtering utilities
│   ├── index.ts  # Re-export all filtering utilities
│   ├── services.ts  # Service-specific filtering utilities
│   ├── projects.ts  # Project-specific filtering utilities
│   └── seasonal.ts  # Season-specific utilities
├── formatting.ts # Formatting utilities
├── validation.ts # Validation utilities
└── index.ts      # Re-export all utilities
```

### 3.4. Consistent Directory Depth

**Issue**: Inconsistent directory depth across the codebase.

**Recommendation**: Establish and document standard directory depths:

```
# Standard Directory Depths

1. src/                       # Source root
2. src/ui/                    # UI component category
3. src/ui/Button/             # Specific component
4. src/ui/Button/index.tsx    # Component implementation

1. src/sections/              # Sections root
2. src/sections/10-home/      # Section category
3. src/sections/10-home/components/ # Section-specific components
4. src/sections/10-home/components/Feature.tsx # Component implementation
```

### 3.5. API and Filtering Separation

**Issue**: API layer contains filtering logic that should be in the filtering module.

**Recommendation**: Move all filtering logic from the API layer to the filtering module:

```
src/lib/
├── api/
│   ├── index.ts   # API exports
│   ├── enhanced.ts # API implementation with caching (no filtering)
│   └── sync.ts    # Synchronous API utilities
└── utils/
    ├── filtering/ # All filtering logic moved here
    └── index.ts   # Utility exports
```

## 4. Practical Implementation Steps

To implement these recommendations while maintaining codebase stability:

### 4.1. Document the Current Structure

Create a comprehensive documentation of the current filestructure to serve as a reference point:

```markdown
# Codebase Structure Documentation

This document outlines the current structure of the Ringerike Landskap website codebase,
serving as a reference point for understanding the organization and relationships between files.

## Directory Structure

- `src/`: Source code root
  - `app/`: Application entry point
  - `components/`: Specialized components
  - ...

## Key Relationships

- Services and Projects have a many-to-many relationship
- Seasonal data affects both Services and Projects
- ...

## Naming Conventions

- React components use PascalCase (Button.tsx)
- Utilities use camelCase (filtering.ts)
- ...
```

### 4.2. Establish Filestructure Guidelines

Create a document that clearly defines the rules for filestructure organization:

```markdown
# Filestructure Guidelines

This document defines the rules and conventions for organizing files in the Ringerike Landskap website codebase.

## Directory Organization

1. Group files by domain, not technical function
2. Use consistent naming conventions
3. Limit directory depth to 4 levels
4. ...

## File Locations

1. Reusable UI components go in `src/ui/`
2. Page sections go in `src/sections/{section-number}-{section-name}/`
3. ...

## Import Patterns

1. Use absolute imports with `@/` alias
2. Group imports by external/internal
3. ...
```

### 4.3. Create a Migration Plan

Develop a step-by-step plan for migrating to the improved filestructure:

```markdown
# Filestructure Migration Plan

This document outlines the step-by-step process for improving the filestructure of the Ringerike Landskap website codebase.

## Phase 1: Component Organization

1. Move all reusable UI components to `src/ui/`
2. Update imports to reflect new locations
3. ...

## Phase 2: Utility Organization

1. Split filtering.ts into domain-specific modules
2. Move seasonal utilities to dedicated module
3. ...

## Phase 3: Documentation

1. Create a filestructure visualization
2. Add README.md files to key directories
3. ...
```

### 4.4. Implement Changes Incrementally

Make changes in small, focused commits that can be easily reviewed:

1. **Start with Documentation**: Add READMEs and guidelines first.
2. **Move Files**: Move files to their new locations without changing their content.
3. **Update Imports**: Update imports to reflect the new file locations.
4. **Refactor Content**: Only after the structure is stable, refactor the content of files.

## 5. Monitoring and Maintenance

### 5.1. Filestructure Visualization

Regularly generate and update visualizations of the filestructure to track changes and identify potential issues:

```bash
# Generate a directory tree visualization
find src -type d | sort | sed -e "s/[^-][^\/]*\//  |/g" -e "s/|\([^ ]\)/|-\1/" > filestructure.txt
```

### 5.2. Directory README Files

Add README.md files to key directories explaining their purpose and organization:

```markdown
# src/lib/utils/filtering

This directory contains utilities for filtering services and projects based on various criteria.

## Files

- `index.ts`: Re-exports all filtering utilities
- `services.ts`: Service-specific filtering utilities
- `projects.ts`: Project-specific filtering utilities
- `seasonal.ts`: Season-specific utilities

## Usage

```typescript
import { filterServices, serviceMatchesCategory } from '@/lib/utils/filtering';
```
```

### 5.3. Regular Structure Reviews

Schedule regular reviews of the filestructure to identify and address issues:

1. **Monthly Structure Review**: Check for adherence to guidelines.
2. **Quarterly Deep Dive**: Analyze cross-directory dependencies and identify opportunities for improvement.
3. **Pre-Release Structure Audit**: Before major releases, audit the structure for consistency.

## 6. Key Filestructure Patterns to Enforce

### 6.1. Component Directory Pattern

Each significant UI component should have its own directory with a consistent structure:

```
Button/
├── index.tsx     # Main component implementation
├── Button.test.tsx # Component tests
└── Button.README.md # Component documentation
```

### 6.2. Section Organization Pattern

Section-specific components should be organized in a consistent way:

```
10-home/
├── index.tsx       # Main section component
├── components/     # Section-specific components
│   ├── Feature.tsx
│   └── Hero.tsx
└── hooks/          # Section-specific hooks
    └── useHomeData.ts
```

### 6.3. Barrel Export Pattern

Use index.ts files to re-export from directories, providing a clean public API:

```typescript
// src/ui/index.ts
export { Button } from './Button';
export { Card } from './Card';
export { Container } from './Container';
// ...
```

### 6.4. Domain Configuration Pattern

Group domain-specific configuration files together:

```
lib/config/
├── index.ts      # Re-export all configuration
├── images.ts     # Image paths configuration
├── paths.ts      # URL paths configuration
└── site.ts       # General site configuration
```

## 7. Example: Ideal Directory Structure

Based on the filestructure-first approach, here's an example of an ideal directory structure for the Ringerike Landskap website:

```
website/
├── config/           # Environment-specific configuration 
├── memorybank/       # Project documentation
├── public/           # Static assets organized by type
├── scripts/          # Utility scripts and tools
└── src/              # Source code
    ├── app/          # Application entry point
    │   └── README.md # App component documentation
    ├── components/   # Cross-cutting specialized components
    │   └── README.md # Components documentation
    ├── content/      # Dynamic, user-facing content
    │   └── README.md # Content documentation
    ├── data/         # Static, application-wide data
    │   └── README.md # Data documentation
    ├── layout/       # Layout components
    │   └── README.md # Layout documentation
    ├── lib/          # Core utilities and logic
    │   ├── api/      # API client code
    │   ├── config/   # Configuration
    │   ├── constants/# Application constants
    │   ├── context/  # React context providers
    │   ├── hooks/    # Custom React hooks
    │   ├── types/    # TypeScript definitions
    │   ├── utils/    # Utility functions organized by domain
    │   │   ├── filtering/ # Domain-specific filtering utilities
    │   │   └── README.md  # Utils documentation
    │   └── README.md # Lib documentation
    ├── sections/     # Page sections in logical order
    │   ├── 10-home/  # Each section with consistent structure
    │   ├── 20-about/
    │   └── README.md # Sections documentation
    ├── styles/       # Global styles
    │   └── README.md # Styles documentation
    ├── ui/           # Reusable UI components
    │   └── README.md # UI documentation
    └── README.md     # Source code documentation
```

## 8. Conclusion

By adopting a filestructure-first approach to codebase organization, the Ringerike Landskap website can maintain a clear, consistent, and maintainable architecture. This approach:

1. Treats the directory structure as the single source of truth
2. Enforces consistent organization and naming conventions
3. Groups files by domain rather than technical function
4. Makes relationships between components explicit
5. Simplifies navigation and understanding of the codebase

These recommendations provide a practical path toward implementing these principles while maintaining codebase stability and developer productivity.

Ready to establish order from the root of this codebase by prioritizing the filestructure as the fundamental representation of the system's architecture.
