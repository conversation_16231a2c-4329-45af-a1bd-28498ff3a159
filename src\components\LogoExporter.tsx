import { useState, useEffect } from 'react';
import { Card } from '@/ui/Card';
import { Button } from '@/ui/Button';
import { Select } from '@/ui/Form';
import { Container } from '@/ui/Container';
import { Download, Palette } from 'lucide-react';

interface LogoConfig {
  size: string;
  format: string;
  style: string;
  variant: string;
}

const LogoExporter = () => {
  const [config, setConfig] = useState<LogoConfig>({
    size: '2K',
    format: 'png',
    style: 'Light',
    variant: 'layout1'
  });

  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [filename, setFilename] = useState<string>('');

  // Options for each parameter
  const sizes = [
    { value: '1K', label: '1K' },
    { value: '2K', label: '2K' },
    { value: '4K', label: '4K' },
    { value: '8K', label: '8K' }
  ];

  const formats = [
    { value: 'svg', label: '.SVG' },
    { value: 'png', label: '.PNG' },
    { value: 'jpg', label: '.JPG' }
  ];

  const styles = [
    { value: 'Light', label: 'Lys' },
    { value: 'Dark', label: 'Mørk' }
  ];

  const variants = [
    { value: 'layout1', label: 'Layout 1' },
    { value: 'layout2', label: 'Layout 2' },
    { value: 'layout3', label: 'Layout 3' }
  ];

  // Generate filename and preview URL
  useEffect(() => {
    const styleName = config.style.toLowerCase();
    const name = `RingerikeLandskap_logo_${config.variant}_${styleName}_${config.size}.${config.format}`;
    setFilename(name);

    // Set preview URL to actual logo file
    const previewPath = `/logos/${config.variant}/${styleName === 'light' ? 'bright' : 'dark'}.svg`;
    setPreviewUrl(previewPath);
  }, [config]);

  const handleConfigChange = (key: keyof LogoConfig, value: string) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDownload = async () => {
    try {
      const styleName = config.style.toLowerCase() === 'light' ? 'bright' : 'dark';
      const logoPath = `/logos/${config.variant}/${styleName}.svg`;

      if (config.format === 'svg') {
        // Direct SVG download
        const response = await fetch(logoPath);
        if (!response.ok) throw new Error(`SVG not found: ${response.status}`);

        const blob = await response.blob();
        downloadBlob(blob, filename);
      } else {
        // Convert to PNG/JPG using canvas
        await convertSVGToRaster(logoPath, config.format, filename);
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }
  };

  const downloadBlob = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const convertSVGToRaster = async (svgPath: string, format: string, filename: string) => {
    // Fetch SVG
    const response = await fetch(svgPath);
    if (!response.ok) throw new Error(`SVG not found: ${response.status}`);

    const svgText = await response.text();

    // Get dimensions
    const sizeMap = { '1K': 1024, '2K': 2048, '4K': 4096, '8K': 8192 };
    const size = sizeMap[config.size as keyof typeof sizeMap] || 2048;

    // Create canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    canvas.width = size;
    canvas.height = size;

    // Set background for JPG
    if (format === 'jpg') {
      ctx.fillStyle = config.style === 'Dark' ? '#1e293b' : '#ffffff';
      ctx.fillRect(0, 0, size, size);
    }

    // Create and load image
    const img = document.createElement('img');
    const svgBlob = new Blob([svgText], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(svgBlob);

    return new Promise<void>((resolve, reject) => {
      img.onload = () => {
        ctx.drawImage(img, 0, 0, size, size);

        const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
        canvas.toBlob((blob) => {
          if (blob) {
            downloadBlob(blob, filename);
            resolve();
          } else {
            reject(new Error('Failed to create image'));
          }
        }, mimeType, format === 'jpg' ? 0.95 : undefined);

        URL.revokeObjectURL(url);
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load SVG'));
      };

      img.src = url;
    });
  };

  const getResolutionInfo = (size: string) => {
    const resolutions = {
      '1K': '1024px',
      '2K': '2048px', 
      '4K': '4096px',
      '8K': '8192px'
    };
    return resolutions[size as keyof typeof resolutions] || size;
  };

  return (
    <Container>
      <div className="max-w-4xl mx-auto py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Ringerike Landskap AS</h1>
          <p className="text-gray-600 text-sm sm:text-base">Last ned vår logo i forskjellige formater</p>
        </div>

        {/* Configuration Section */}
        <Card title="Konfigurasjon" className="mb-6">
          <div className="space-y-6">
            {/* Style & Layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Stil"
                options={styles}
                value={config.style}
                onChange={(e) => handleConfigChange('style', e.target.value)}
              />
              <Select
                label="Layout"
                options={variants}
                value={config.variant}
                onChange={(e) => handleConfigChange('variant', e.target.value)}
              />
            </div>

            {/* Format & Size */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Format"
                options={formats}
                value={config.format}
                onChange={(e) => handleConfigChange('format', e.target.value)}
              />
              <Select
                label="Størrelse"
                options={sizes}
                value={config.size}
                onChange={(e) => handleConfigChange('size', e.target.value)}
              />
            </div>

            {/* Format Info */}
            <div className="text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
              {config.format === 'svg' && '• Vektorformat - uendelig skalerbar'}
              {config.format === 'png' && '• Gjennomsiktig bakgrunn - perfekt for overlegg'}
              {config.format === 'jpg' && '• Inkluderer bakgrunnsfarge fra forhåndsvisning'}
            </div>
          </div>
        </Card>

        {/* Preview Section */}
        <Card title="Logo Forhåndsvisning" className="mb-6">
          <div className={`${
            config.style === 'Dark'
              ? 'bg-gradient-to-br from-slate-800 to-slate-900'
              : 'bg-gradient-to-br from-white to-gray-50'
          } rounded-xl border border-gray-200 p-6 sm:p-8 flex items-center justify-center min-h-[250px] sm:min-h-[300px] md:min-h-[350px]`}>
            {previewUrl ? (
              <div className="w-full h-full flex items-center justify-center max-w-md mx-auto">
                <img
                  src={previewUrl}
                  alt={`Ringerike Landskap Logo - ${config.style} Style, Variant ${config.variant.toUpperCase()}`}
                  className="max-w-full max-h-full object-contain w-auto h-auto"
                  onError={() => {
                    console.error('Logo preview failed to load:', previewUrl);
                  }}
                />
              </div>
            ) : (
              <div className="text-center space-y-3">
                <div className="w-12 h-12 bg-gray-200 rounded-lg mx-auto flex items-center justify-center">
                  <Palette className="h-6 w-6 text-gray-400" />
                </div>
                <p className="font-medium text-sm text-gray-500">Laster forhåndsvisning...</p>
              </div>
            )}
          </div>
        </Card>

        {/* Download Section */}
        <Card title="Nedlasting" className="mb-6">
          <div className="space-y-4">
            {/* Download Button */}
            <Button
              onClick={handleDownload}
              variant="primary"
              size="lg"
              className="w-full"
            >
              <Download className="h-5 w-5 mr-2" />
              Last ned logo
            </Button>

            {/* File Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700 block mb-1">Generert filnavn:</label>
                  <div className="bg-white p-3 rounded border border-gray-200">
                    <code className="text-sm text-gray-800 break-all">{filename}</code>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600 block">Format</span>
                    <span className="text-gray-900">{config.format.toUpperCase()}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Størrelse</span>
                    <span className="text-gray-900">{config.format === 'svg' ? 'Vektor' : getResolutionInfo(config.size)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Stil</span>
                    <span className="text-gray-900">{config.style === 'Light' ? 'Lys' : 'Mørk'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 block">Layout</span>
                    <span className="text-gray-900">{config.variant.replace('layout', 'Layout ')}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </Container>
  );
};

export default LogoExporter;
