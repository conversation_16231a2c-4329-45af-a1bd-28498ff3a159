import { useState, useCallback, useRef, useEffect } from 'react';
import { TemporaryEndDateController } from '@/lib/meta/utils/TemporaryEndDateController';

interface UseSmartTemporaryEndDateProps {
  value?: string;
  onChange?: (value: string) => void;
  startDate?: string; // The employment start date constraint
}

export const useSmartTemporaryEndDate = ({ value = '', onChange, startDate }: UseSmartTemporaryEndDateProps) => {
  const [displayValue, setDisplayValue] = useState(value);
  const controllerRef = useRef(new TemporaryEndDateController(startDate));
  const inputRef = useRef<HTMLInputElement>(null);

  const controller = controllerRef.current;

  // Update controller when start date changes
  useEffect(() => {
    controller.updateStartDate(startDate);
  }, [startDate, controller]);

  // Sync external value changes
  useEffect(() => {
    setDisplayValue(value);
  }, [value]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    const cursorPosition = input.selectionStart || 0;
    
    const newValue = controller.handleArrowKeyNavigation(
      e.key,
      displayValue,
      cursorPosition
    );

    if (newValue !== null) {
      e.preventDefault(); // Prevent default arrow key behavior
      setDisplayValue(newValue);
      onChange?.(newValue);
      
      // For date inputs, we can't use setSelectionRange, so just focus the input
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 0);
    }
  }, [displayValue, onChange, controller]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setDisplayValue(newValue);
    onChange?.(newValue);
  }, [onChange]);

  const handleBlur = useCallback(() => {
    const correctedValue = controller.validateAndCorrect(displayValue);
    if (correctedValue !== displayValue) {
      setDisplayValue(correctedValue);
      onChange?.(correctedValue);
    }
  }, [displayValue, onChange, controller]);

  return {
    value: displayValue,
    onChange: handleChange,
    onKeyDown: handleKeyDown,
    onBlur: handleBlur,
    inputRef,
    ...controller.getHTMLAttributes()
  };
};
