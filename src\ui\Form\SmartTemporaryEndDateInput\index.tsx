import React from 'react';
import { cn } from '@/lib/utils';
import { useSmartTemporaryEndDate } from '@/lib/hooks/useSmartTemporaryEndDate';

interface SmartTemporaryEndDateInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
  label?: string;
  error?: string;
  helper?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  value?: string;
  onChange?: (value: string) => void;
  startDate?: string; // The employment start date constraint
}

const SmartTemporaryEndDateInput = React.forwardRef<HTMLInputElement, SmartTemporaryEndDateInputProps>(
  ({ label, error, helper, leftIcon, rightIcon, className, value, onChange, startDate, ...props }, ref) => {
    // Ensure we have an id for accessibility
    const id = props.id || props.name || `temp-end-date-${Math.random().toString(36).substr(2, 9)}`;

    const {
      value: displayValue,
      onChange: handleChange,
      onKeyDown: handleKeyDown,
      onBlur: handleBlur,
      inputRef,
      min,
      max,
      placeholder
    } = useSmartTemporaryEndDate({ value, onChange, startDate });

    // Merge refs
    const mergedRef = React.useCallback((node: HTMLInputElement) => {
      // Set the hook's ref
      if (inputRef.current !== node) {
        (inputRef as React.MutableRefObject<HTMLInputElement | null>).current = node;
      }
      // Set the forwarded ref
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    }, [ref, inputRef]);

    // Generate helper text based on start date (prioritize passed helper, then dynamic)
    const effectiveHelper = React.useMemo(() => {
      if (helper) return helper;
      if (startDate) {
        return `Må være etter startdato (${startDate})`;
      }
      return undefined;
    }, [helper, startDate]);

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={id}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
            <span className="text-xs text-gray-500 ml-2 font-normal">
              (dd.mm.yyyy)
            </span>
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
              {leftIcon}
            </div>
          )}
          <input
            ref={mergedRef}
            id={id}
            type="date"
            value={displayValue}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            min={min}
            max={max}
            placeholder={placeholder}
            className={cn(
              'w-full rounded-md shadow-sm transition-colors duration-200',
              'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              className
            )}
            {...props}
          />
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>
        {(error || effectiveHelper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || effectiveHelper}
          </p>
        )}
        <div className="text-xs text-gray-500 mt-1">
          💡 Tip: Use arrow keys to navigate between day/month/year fields
        </div>
      </div>
    );
  }
);

SmartTemporaryEndDateInput.displayName = 'SmartTemporaryEndDateInput';

export { SmartTemporaryEndDateInput };
