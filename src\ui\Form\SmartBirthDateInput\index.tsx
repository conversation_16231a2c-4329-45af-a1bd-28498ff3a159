import React from 'react';
import { cn } from '@/lib/utils';
import { useSmartBirthDate } from '@/lib/hooks/useSmartBirthDate';

interface SmartBirthDateInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange'> {
  label?: string;
  error?: string;
  helper?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  value?: string;
  onChange?: (value: string) => void;
}

const SmartBirthDateInput = React.forwardRef<HTMLInputElement, SmartBirthDateInputProps>(
  ({ label, error, helper, leftIcon, rightIcon, className, value, onChange, ...props }, ref) => {
    // Ensure we have an id for accessibility
    const id = props.id || props.name || `birth-date-${Math.random().toString(36).substr(2, 9)}`;

    const {
      value: displayValue,
      onChange: handleChange,
      onKeyDown: handleKeyDown,
      onBlur: handleBlur,
      inputRef,
      min,
      max,
      placeholder
    } = useSmartBirthDate({ value, onChange });

    // Merge refs
    const mergedRef = React.useCallback((node: HTMLInputElement) => {
      // Set the hook's ref
      if (inputRef.current !== node) {
        (inputRef as React.MutableRefObject<HTMLInputElement | null>).current = node;
      }
      // Set the forwarded ref
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    }, [ref, inputRef]);

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={id}
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            {label}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
              {leftIcon}
            </div>
          )}
          <input
            ref={mergedRef}
            id={id}
            type="date"
            value={displayValue}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            min={min}
            max={max}
            placeholder={placeholder}
            className={cn(
              'w-full rounded-md shadow-sm transition-colors duration-200',
              'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              className
            )}
            {...props}
          />
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
        <div className="text-xs text-gray-500 mt-1">
          💡 Tip: Use arrow keys to navigate between day/month/year fields (1925-2010)
        </div>
      </div>
    );
  }
);

SmartBirthDateInput.displayName = 'SmartBirthDateInput';

export { SmartBirthDateInput };
