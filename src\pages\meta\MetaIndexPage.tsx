import { Meta } from '@/layout/Meta';
import { Container } from '@/ui/Container';
import { Card } from '@/ui/Card';
import { Link } from 'react-router-dom';
import { Download, FileText, Settings } from 'lucide-react';

const MetaIndexPage = () => {
  const utilities = [
    {
      id: 'logo',
      title: 'Logo Generator',
      description: 'Last ned Ringerike Landskap logoer i ulike formater og størrelser.',
      icon: Download,
      path: '/meta/logo',
      status: 'active',
      category: 'Brand Assets'
    },
    {
      id: 'arbeidskontrakt',
      title: 'Arbeidskontrakt Generator',
      description: 'Generer juridisk korrekte arbeidskontrakter i henhold til Arbeidsmiljøloven.',
      icon: FileText,
      path: '/meta/arbeidskontrakt',
      status: 'active',
      category: 'HR Tools'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Aktiv</span>;
      case 'development':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Under utvikling</span>;
      default:
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Ukjent</span>;
    }
  };

  return (
    <>
      <Meta
        title="Meta Utilities - Ringerike Landskap"
        description="Interne verktøy og utilities for Ringerike Landskap AS. Tilgang til brand assets, administrative verktøy og andre interne ressurser."
        keywords={['meta', 'utilities', 'verktøy', 'admin', 'Ringerike Landskap']}
      />
      <Container>
        <div className="max-w-6xl mx-auto py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Settings className="h-8 w-8 text-green-600 mr-3" />
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
                Meta Utilities
              </h1>
            </div>
            <p className="text-gray-600 text-sm sm:text-base max-w-2xl mx-auto">
              Interne verktøy og utilities for Ringerike Landskap AS. 
              Disse verktøyene er designet for å støtte daglige operasjoner og administrative oppgaver.
            </p>
          </div>

          {/* Utilities Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {utilities.map((utility) => {
              const IconComponent = utility.icon;
              
              return (
                <Card
                  key={utility.id}
                  title=""
                  className="hover:shadow-lg transition-shadow duration-200"
                >
                  <Link to={utility.path} className="block">
                    <div className="p-6">
                      {/* Icon and Status */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center">
                          <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <IconComponent className="h-6 w-6 text-green-600" />
                          </div>
                        </div>
                        {getStatusBadge(utility.status)}
                      </div>

                      {/* Content */}
                      <div>
                        <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">
                          {utility.category}
                        </div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {utility.title}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-3">
                          {utility.description}
                        </p>
                      </div>

                      {/* Action */}
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <span className="text-green-600 text-sm font-medium hover:text-green-700">
                          {utility.status === 'active' ? 'Åpne verktøy' : 'Se detaljer'} →
                        </span>
                      </div>
                    </div>
                  </Link>
                </Card>
              );
            })}
          </div>

          {/* Info Section */}
          <div className="mt-12">
            <Card title="Om Meta Utilities" className="bg-gray-50">
              <div className="space-y-4 text-sm text-gray-600">
                <p>
                  Meta utilities er interne verktøy designet spesifikt for Ringerike Landskap AS. 
                  Disse verktøyene støtter ulike aspekter av bedriftens drift, fra brand management til administrative oppgaver.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Tilgjengelige kategorier:</h4>
                    <ul className="space-y-1">
                      <li>• Brand Assets (logoer, fargepaletter)</li>
                      <li>• HR Tools (kontrakter, dokumenter)</li>
                      <li>• Administrative verktøy</li>
                      <li>• Rapportering og analyse</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Status forklaring:</h4>
                    <ul className="space-y-1">
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                        Aktiv - Fullt funksjonell
                      </li>
                      <li className="flex items-center">
                        <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                        Under utvikling - Kommer snart
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </Container>
    </>
  );
};

export default MetaIndexPage;
