import { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Button } from '@/ui';
import { AlertTriangle, Home, RefreshCw } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Props {
  children: ReactNode;
  fallbackTitle?: string;
  fallbackMessage?: string;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * Error Boundary specifically for Meta Utilities
 * Prevents meta utility errors from crashing the main website
 */
class MetaErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Meta Utility Error:', error);
      console.error('Error Info:', errorInfo);
    }

    // Update state with error details
    this.setState({
      error,
      errorInfo
    });

    // In production, you might want to log this to an error reporting service
    // Example: logErrorToService(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      const { fallbackTitle = "Meta Utility Error", fallbackMessage } = this.props;
      
      return (
        <Container>
          <div className="max-w-2xl mx-auto py-16">
            <Card title="" className="text-center">
              <div className="p-8">
                {/* Error Icon */}
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                  </div>
                </div>

                {/* Error Message */}
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  {fallbackTitle}
                </h2>
                
                <p className="text-gray-600 mb-6">
                  {fallbackMessage || 
                    "Det oppstod en feil med dette verktøyet. Hovednettsiden fungerer fortsatt normalt."
                  }
                </p>

                {/* Development Error Details */}
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <div className="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                    <h3 className="font-medium text-gray-900 mb-2">Development Error Details:</h3>
                    <pre className="text-xs text-gray-700 overflow-auto">
                      {this.state.error.toString()}
                      {this.state.errorInfo?.componentStack}
                    </pre>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    onClick={this.handleRetry}
                    className="flex items-center justify-center"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Prøv igjen
                  </Button>
                  
                  <Link to="/meta">
                    <Button variant="outline" className="flex items-center justify-center w-full">
                      <Home className="h-4 w-4 mr-2" />
                      Tilbake til Meta Utilities
                    </Button>
                  </Link>
                  
                  <Link to="/">
                    <Button variant="outline" className="flex items-center justify-center w-full">
                      <Home className="h-4 w-4 mr-2" />
                      Tilbake til hovedsiden
                    </Button>
                  </Link>
                </div>

                {/* Reassurance Message */}
                <div className="mt-8 p-4 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-800">
                    <strong>Viktig:</strong> Denne feilen påvirker kun dette interne verktøyet. 
                    Hovednettsiden og alle andre funksjoner fungerer normalt.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default MetaErrorBoundary;
