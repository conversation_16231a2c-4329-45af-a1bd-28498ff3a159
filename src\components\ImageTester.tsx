import React, { useState, useEffect } from 'react';
import { getImagesFromCategory, GeoImage } from '@/lib/utils/images';
import { encodeImagePath } from '@/lib/utils/paths';
import { IMAGE_CATEGORIES } from '@/lib/config/images';

/**
 * A component to test the image loading functionality
 */
const ImageTester: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<keyof typeof IMAGE_CATEGORIES>('belegg');
  const [images, setImages] = useState<GeoImage[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      const categoryImages = getImagesFromCategory(selectedCategory);
      setImages(categoryImages);
      setError(null);
    } catch (err) {
      setError(`Error loading images: ${err instanceof Error ? err.message : String(err)}`);
      setImages([]);
    }
  }, [selectedCategory]);

  return (
    <div className="p-6">
      <h2 className="text-2xl font-bold mb-4">Image Tester</h2>
      
      {/* Category selector */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Category:
        </label>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value as keyof typeof IMAGE_CATEGORIES)}
          className="block w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
        >
          {Object.keys(IMAGE_CATEGORIES).map((category) => (
            <option key={category} value={category}>
              {IMAGE_CATEGORIES[category as keyof typeof IMAGE_CATEGORIES]}
            </option>
          ))}
        </select>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Image grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {images.map((image, index) => (
          <div key={index} className="border rounded-lg overflow-hidden shadow-sm">
            <div className="aspect-square relative">
              <img
                src={encodeImagePath(image.path)}
                alt={image.metadata?.title || ''}
                className="absolute inset-0 w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.onerror = null;
                  target.src = '/images/placeholder.webp';
                  target.classList.add('bg-gray-200');
                }}
              />
            </div>
            <div className="p-3 bg-white">
              <h3 className="font-medium text-sm">{image.filename}</h3>
              {image.coordinates && (
                <p className="text-xs text-gray-500 mt-1">
                  Coordinates: {image.coordinates.latitude.toFixed(6)}, {image.coordinates.longitude.toFixed(6)}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {images.length === 0 && !error && (
        <div className="text-center py-12 text-gray-500">
          No images found for this category.
        </div>
      )}
    </div>
  );
};

export default ImageTester;
