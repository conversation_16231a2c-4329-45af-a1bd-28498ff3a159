import { useState } from 'react';
import { Card, Container } from '@/ui';
import { CheckCircle, FileText, User, Settings } from 'lucide-react';
import { ContractFormData, initialContractFormData } from '@/lib/meta/types';
import BasicInfoStep from './steps/BasicInfoStep';
import AdvancedSettingsStep from './steps/AdvancedSettingsStep';
import ContractGenerationStep from './steps/ContractGenerationStep';

const ArbeidskontraktGenerator = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<ContractFormData>(initialContractFormData);

  const updateFormData = (updates: Partial<ContractFormData>) => {
    setFormData((prev: ContractFormData) => ({ ...prev, ...updates }));
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  const steps = [
    {
      number: 1,
      title: "Grunnleggende informasjon",
      description: "Personopplysninger og stillingsinformasjon",
      icon: User,
      completed: currentStep > 1
    },
    {
      number: 2,
      title: "Avanserte innstillinger",
      description: "Bedriftsinformasjon og arbeidsvilkår",
      icon: Settings,
      completed: currentStep > 2
    },
    {
      number: 3,
      title: "Kontraktgenerering",
      description: "Sammendrag og generering av kontrakt",
      icon: FileText,
      completed: false
    }
  ];

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <BasicInfoStep
            formData={formData}
            updateFormData={updateFormData}
            onNext={nextStep}
          />
        );
      case 2:
        return (
          <AdvancedSettingsStep
            formData={formData}
            updateFormData={updateFormData}
            onNext={nextStep}
            onPrev={prevStep}
          />
        );
      case 3:
        return (
          <ContractGenerationStep
            formData={formData}
            updateFormData={updateFormData}
            onPrev={prevStep}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Container>
      <div className="max-w-6xl mx-auto py-8 sm:py-12">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
            Arbeidskontrakt Generator
          </h1>
          <p className="text-gray-600 text-sm sm:text-base px-2">
            Generer juridisk korrekte arbeidskontrakter for Ringerike Landskap AS
          </p>
        </div>

        {/* Progress Steps */}
        <Card title="" className="mb-6 sm:mb-8">
          <div className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
              {steps.map((step, index) => {
                const IconComponent = step.icon;
                const isActive = currentStep === step.number;
                const isCompleted = step.completed;

                return (
                  <div key={step.number} className="flex items-center mb-4 sm:mb-0">
                    {/* Step Circle */}
                    <button
                      onClick={() => goToStep(step.number)}
                      className={`
                        flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full border-2 transition-colors flex-shrink-0
                        ${isActive
                          ? 'bg-green-600 border-green-600 text-white'
                          : isCompleted
                            ? 'bg-green-100 border-green-600 text-green-600'
                            : 'bg-gray-100 border-gray-300 text-gray-400'
                        }
                      `}
                    >
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6" />
                      ) : (
                        <IconComponent className="h-5 w-5 sm:h-6 sm:w-6" />
                      )}
                    </button>

                    {/* Step Info */}
                    <div className="ml-3 sm:ml-4 text-left">
                      <div className={`text-sm font-medium ${isActive ? 'text-green-600' : 'text-gray-900'}`}>
                        Steg {step.number}
                      </div>
                      <div className={`text-xs sm:text-sm ${isActive ? 'text-green-600' : 'text-gray-600'}`}>
                        {step.title}
                      </div>
                    </div>

                    {/* Connector Line - only on desktop */}
                    {index < steps.length - 1 && (
                      <div className={`
                        hidden sm:block flex-1 h-0.5 mx-4 lg:mx-6
                        ${step.completed ? 'bg-green-600' : 'bg-gray-300'}
                      `} />
                    )}
                  </div>
                );
              })}
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
              />
            </div>
          </div>
        </Card>

        {/* Step Content */}
        <div className="min-h-[400px] sm:min-h-[600px]">
          {renderStep()}
        </div>
      </div>
    </Container>
  );
};

export default ArbeidskontraktGenerator;
